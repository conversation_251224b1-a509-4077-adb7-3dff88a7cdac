import { useState, useEffect, useCallback } from "react";
// Components
import { CookieConsentBanner } from "./components/CookieConsentBanner";
import { AudioPermissionBanner } from "./components/AudioPermissionBanner";

import { Header } from "./components/Header/Header";
import { GlobalLogs } from "./components/GlobalLogs";
import { Loader } from "./components/Loader";
import MainView from "./components/views/MainView";
import PlayView from "./components/views/PlayView";
import RulesView from "./components/views/RulesView";

// Contexts
import { useAppContext } from "./contexts/AppContext";
import { useEnygmaGame } from "./contexts/EnygmaGameContext";
import { useGameOrchestrator } from "./contexts/GameOrchestratorContext";
import { useSpeechOutput } from "./contexts/SpeechOutputContext";
// Types
import type { GameMode } from "./services/AIService";
// Styles
import "./App.scss";
import LivesView from "./components/views/LivesView";
import CluesView from "./components/views/CluesView";

// 🆕 Nuevo tipo para controlar las vistas
type ViewMode = "main" | "play" | "rules" | "lives" | "clues";

function App() {
  const { isInitialized, errors } = useAppContext();
  const [, setAudioActivated] = useState(false);

  // 🔧 CAMBIO: Usar los nuevos contextos
  const {} = useEnygmaGame(); // ✅ En lugar de useAIContext
  const { startGameFlow, isReady, setupProgress } = useGameOrchestrator();
  const {
    speakGameMessage,
    state: { isReady: speechReady, isConfiguring },
  } = useSpeechOutput();

  const [hasPlayedWelcome, setHasPlayedWelcome] = useState<boolean>(false);
  const [isStartingGame, setIsStartingGame] = useState<boolean>(false);
  const [showAudioBanner, setShowAudioBanner] = useState<boolean>(false);

  // 🆕 Estado para controlar qué vista mostrar
  const [currentView, setCurrentView] = useState<ViewMode>("main");

  // ========== EFECTOS ==========
  useEffect(() => {
    // La inicialización ahora se maneja en AppContext
    if (isInitialized) {
      // console.log("✅ App: Aplicación inicializada correctamente");
    }
  }, [isInitialized]);

  // Mostrar errores si los hay
  useEffect(() => {
    if (errors.length > 0) {
      // console.warn("⚠️ App: Errores detectados:", errors);
    }
  }, [errors]);

  // Verificar si se necesita mostrar banner de audio
  useEffect(() => {
    const hasAudioActivated = localStorage.getItem("enygma_audio_activated");
    const hasConsent = localStorage.getItem("enygma_analytics_consent");

    // Solo mostrar banner propio si no hay sistema de cookies
    if (hasAudioActivated === "true") {
      setHasPlayedWelcome(true);
      setShowAudioBanner(false);
    } else if (!hasConsent) {
      // Banner de cookies se encargará del audio
      setShowAudioBanner(false);
    } else {
      // Mostrar banner de audio simple
      setShowAudioBanner(true);
    }
  }, []);

  // ========== EFECTO PARA NARRACIÓN DE BIENVENIDA ==========
  useEffect(() => {
    const playWelcomeMessage = () => {
      if (hasPlayedWelcome || !isReady || !speechReady) {
        return;
      }

      // console.log(
      //   "🎵 Audio preparado - requiere interacción del usuario para reproducir"
      // );
    };

    const timer = setTimeout(playWelcomeMessage, 1000);
    return () => clearTimeout(timer);
  }, [hasPlayedWelcome, isReady, speechReady]);

  // ========== FUNCIONES DE AUDIO ==========
  // 🔧 MEJORADO: Usar speakGameMessage en lugar de speakWithAutoConfig
  const playAudio = useCallback(
    async (text: string) => {
      try {
        await speakGameMessage(text, "system");
      } catch (error) {
        // console.error("❌ Error reproduciendo audio:", error);
        throw error;
      }
    },
    [speakGameMessage]
  );

  // Nueva función para activar audio con interacción del usuario
  const activateAudioWithWelcome = useCallback(async () => {
    try {
      await playAudio("Bienvenido a Enygma, ¿lo adivinas?");
      setHasPlayedWelcome(true);
      setShowAudioBanner(false);
      localStorage.setItem("enygma_audio_activated", "true");
    } catch (error) {
      // console.error("❌ Error activando audio:", error);
    }
  }, [playAudio]);

  // 🔧 CAMBIO: Usar startGameFlow del orchestrator en lugar de generateResponse
  const handleStartGame = async (mode: GameMode) => {
    setIsStartingGame(true);

    try {
      // Activar audio con bienvenida si no se ha reproducido aún
      if (!hasPlayedWelcome) {
        await activateAudioWithWelcome();
      }

      // 🔧 CAMBIO: Usar startGameFlow en lugar de startGame + generateResponse
      await startGameFlow(mode);

      // 🆕 Navegar a la vista de juego
      setCurrentView("play");
      // console.log("🎮 Juego iniciado con modo:", mode);
    } catch (error) {
      // console.error("Error al iniciar el juego:", error);
    } finally {
      setIsStartingGame(false);
    }
  };

  const handleToggleSound = () => {
    // console.log("Toggle sound");
  };

  const handleGoHome = () => {
    // console.log("Go to home");
  };

  const handleShowRules = () => {
    setCurrentView("rules");
  };

  const handleShowLives = () => {
    setCurrentView("lives");
  };

  const handleShowClues = () => {
    setCurrentView("clues");
  };

  const handleExistGame = () => {
    setCurrentView("main");
  }

  const handleBackToMain = () => {
    // TODO: depende si vengo anteriormente desde "main" y navego a "rules", setCurrentView("main")
    // Si vengo desde "play" y navego a "lives" o "clues", debería volver a "play"
    setCurrentView("main");
  };

  // ========== HANDLERS ORIGINALES ==========
  const handleAudioActivated = () => {
    setAudioActivated(true);
    // console.log("✅ Audio activado desde banner de cookies");
  };

  // ========== RENDERIZADO CONDICIONAL ==========
  // 🔧 MEJORADO: Mostrar progreso de inicialización si no está listo
  if (!isReady && setupProgress < 100) {
    return <Loader text={`Inicializando aplicación... ${setupProgress}%`} />;
  }

  const renderContent = () => {
    switch (currentView) {
      case "main":
        return <MainView
          handleStartGame={handleStartGame}
          handleShowRules={handleShowRules}
          isStartingGame={isStartingGame}
          isReady={isReady}
        />;
      case "play":
        return <PlayView
          handleShowLives={handleShowLives}
          handleShowClues={handleShowClues}
          handleExistGame={handleExistGame}
        />;
      case "rules":
        return <RulesView isOpen={true} onClose={handleBackToMain} />;
      case "lives":
        return <LivesView />;
      case "clues":
        return <CluesView />;
      default:
        return <div className="content"></div>;
    }
  };

  return (
    <>
      <div className="App">
        <CookieConsentBanner onAudioActivated={handleAudioActivated} />

        <div className="game-container">
          <img src="assets/game/background.png" alt="Background" className="background" />

          <div className="board">
            <Header
              currentView={currentView}
              onBackToMain={handleBackToMain}
              onToggleSound={handleToggleSound}
              onGoHome={handleGoHome}
              showBackButton={currentView !== "main" && currentView !== "play"}
            />

            {renderContent()}
          </div>
        </div>

        {import.meta.env.VITE_DEBUG === "true" && (
          <GlobalLogs
            maxHeight="400px"
            position="fixed"
            defaultVisible={false}
          />
        )}

        {/* Banner de permisos de audio */}
        {/* <AudioPermissionBanner
          onAudioEnabled={() => {
            // console.log("✅ Audio activado por el usuario");
          }}
        /> */}
      </div>
    </>
  );
}

export default App;
