// contexts/SpeechOutputContext.tsx
import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  type ReactNode,
} from "react";
import { speechService } from "../services/SpeechService";
import { log } from "../services/LogService";

// ========== TYPES ==========
export type VoiceGender = "male" | "female";
export type MessageType = "system" | "question" | "answer" | "hint" | "victory" | "error" | "guess";
export type PlaybackState = "idle" | "playing" | "paused" | "loading" | "error";

export interface SpeechOutputState {
  isReady: boolean;
  isConfiguring: boolean;
  playbackState: PlaybackState;
  currentVoice: string;
  availableVoices: string[];
  volume: number;
  speed: number;
  errorMessage: string | null;
}

export interface SpeechMessage {
  id: string;
  text: string;
  type: MessageType;
  timestamp: Date;
  duration?: number;
  voice?: string;
}

export interface SpeechOutputContextProps {
  // Current state
  state: SpeechOutputState;

  // Basic speech controls
  speak: (text: string) => Promise<void>;
  pause: () => void;
  resume: () => void;
  stop: () => void;

  // Configuration
  configure: (gender: VoiceGender) => Promise<boolean>;
  setVolume: (volume: number) => void;
  setSpeed: (speed: number) => void;

  // Game-specific speech
  speakGameMessage: (message: string, type: MessageType) => Promise<void>;
  speakWithEmotion: (message: string, emotion: "excited" | "thoughtful" | "confident" | "disappointed") => Promise<void>;

  // Character voices
  setCharacterVoice: (character: string) => Promise<boolean>;
  getVoiceForCharacter: (character: string) => VoiceGender;

  // Queue management
  speakQueue: (messages: string[]) => Promise<void>;
  clearQueue: () => void;
  skipCurrent: () => void;

  // Utilities
  getAvailableVoices: () => string[];
  testVoice: (text?: string) => Promise<void>;
  reset: () => void;

  // Message history
  messageHistory: SpeechMessage[];
  getLastMessage: () => SpeechMessage | null;
  replayLastMessage: () => Promise<void>;
}

// ========== CONTEXT ==========
const SpeechOutputContext = createContext<SpeechOutputContextProps | undefined>(undefined);

export const useSpeechOutput = () => {
  const context = useContext(SpeechOutputContext);
  if (!context) {
    throw new Error("useSpeechOutput must be used within SpeechOutputProvider");
  }
  return context;
};

// ========== PROVIDER ==========
export const SpeechOutputProvider = ({ children }: { children: ReactNode }) => {
  // ========== LOCAL STATE ==========
  const [state, setState] = useState<SpeechOutputState>({
    isReady: false,
    isConfiguring: false,
    playbackState: "idle",
    currentVoice: "",
    availableVoices: [],
    volume: 1.0,
    speed: 1.1, // Slightly faster for better UX
    errorMessage: null,
  });

  const [messageHistory, setMessageHistory] = useState<SpeechMessage[]>([]);
  const [messageQueue, setMessageQueue] = useState<string[]>([]);
  const [isProcessingQueue, setIsProcessingQueue] = useState<boolean>(false);

  // ========== CHARACTER VOICE DATABASE ==========
  const CHARACTER_VOICES = {
    // Male characters
    "batman": "male", "superman": "male", "iron man": "male", "thor": "male",
    "captain america": "male", "hulk": "male", "spiderman": "male", "spider-man": "male",
    "wolverine": "male", "deadpool": "male", "joker": "male", "harry potter": "male",
    "gandalf": "male", "yoda": "male", "darth vader": "male", "luke skywalker": "male",
    "indiana jones": "male", "james bond": "male", "sherlock holmes": "male",

    // Female characters
    "wonder woman": "female", "black widow": "female", "catwoman": "female",
    "hermione granger": "female", "leia organa": "female", "princess leia": "female",
    "katniss everdeen": "female", "elsa": "female", "anna": "female", "moana": "female",
    "mulan": "female", "pocahontas": "female", "ariel": "female", "belle": "female",

    // Default patterns
    "man": "male", "boy": "male", "guy": "male", "king": "male", "prince": "male",
    "woman": "female", "girl": "female", "queen": "female", "princess": "female"
  } as const;

  // ========== EFFECTS ==========
  useEffect(() => {
    // log.info("speechOutput", "🔊 Inicializando SpeechOutputProvider");

    // Sincronizar estado inicial con el servicio
    updateStateFromService();

    return () => {
      // log.info("speechOutput", "🧹 Limpiando SpeechOutputProvider");
      stop();
      clearQueue();
    };
  }, []);

  // ========== HELPER FUNCTIONS ==========
  const updateStateFromService = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentVoice: speechService.getCurrentVoiceId(),
      availableVoices: speechService.getAvailableVoicesList(),
      isReady: Boolean(speechService.getCurrentVoiceId()),
    }));
  }, []);

  const createMessage = useCallback((text: string, type: MessageType): SpeechMessage => {
    return {
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text,
      type,
      timestamp: new Date(),
      voice: state.currentVoice,
    };
  }, [state.currentVoice]);

  const addToHistory = useCallback((message: SpeechMessage) => {
    setMessageHistory(prev => [message, ...prev.slice(0, 49)]); // Keep last 50 messages
  }, []);

  // ========== BASIC SPEECH CONTROLS ==========
  const speak = useCallback(async (text: string): Promise<void> => {
    if (!text.trim()) {
      // log.warn("speechOutput", "⚠️ Texto vacío para speak()");
      return;
    }

    // log.info("speechOutput", `🗣️ Speaking: ${text.substring(0, 50)}...`);

    setState(prev => ({ ...prev, playbackState: "loading", errorMessage: null }));

    try {
      const startTime = Date.now();

      // Usar speechService directamente
      await speechService.speak(text);

      const duration = Date.now() - startTime;
      const message = createMessage(text, "system");
      message.duration = duration;
      addToHistory(message);

      setState(prev => ({ ...prev, playbackState: "playing" }));

      // Simular finalización después de un tiempo estimado
      const estimatedDuration = text.length * 50; // ~50ms por caracter
      setTimeout(() => {
        setState(prev =>
          prev.playbackState === "playing"
            ? { ...prev, playbackState: "idle" }
            : prev
        );
      }, estimatedDuration);

      // log.success("speechOutput", "✅ Speech completado", { duration });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Error desconocido";
      // log.error("speechOutput", "❌ Error en speak()", error);

      setState(prev => ({
        ...prev,
        playbackState: "error",
        errorMessage
      }));

      throw error;
    }
  }, [createMessage, addToHistory]);

  const pause = useCallback(() => {
    // log.info("speechOutput", "⏸️ Pausando audio");
    speechService.stopSpeech();
    setState(prev => ({ ...prev, playbackState: "paused" }));
  }, []);

  const resume = useCallback(() => {
    // log.info("speechOutput", "▶️ Reanudando audio");
    speechService.playSpeech();
    setState(prev => ({ ...prev, playbackState: "playing" }));
  }, []);

  const stop = useCallback(() => {
    // log.info("speechOutput", "⏹️ Deteniendo audio");
    speechService.noSpeech();
    setState(prev => ({ ...prev, playbackState: "idle" }));
  }, []);

  // ========== CONFIGURATION ==========
  const configure = useCallback(async (gender: VoiceGender): Promise<boolean> => {
    // log.info("speechOutput", `🔧 Configurando voz: ${gender}`);

    setState(prev => ({ ...prev, isConfiguring: true, errorMessage: null }));

    try {
      const success = await speechService.configVoice(gender);

      if (success) {
        updateStateFromService();
        // log.success("speechOutput", `✅ Voz configurada: ${gender}`);
      } else {
        throw new Error(`No se pudo configurar voz ${gender}`);
      }

      return success;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Error configurando voz";
      // log.error("speechOutput", "❌ Error configurando voz", error);

      setState(prev => ({
        ...prev,
        errorMessage
      }));

      return false;

    } finally {
      setState(prev => ({ ...prev, isConfiguring: false }));
    }
  }, [updateStateFromService]);

  const setVolume = useCallback((volume: number) => {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    setState(prev => ({ ...prev, volume: clampedVolume }));

    // Aplicar al elemento de audio si existe
    const audioElement = document.getElementById("audio") as HTMLAudioElement;
    if (audioElement) {
      audioElement.volume = clampedVolume;
    }

    // log.debug("speechOutput", `🔊 Volumen ajustado: ${clampedVolume}`);
  }, []);

  const setSpeed = useCallback((speed: number) => {
    const clampedSpeed = Math.max(0.5, Math.min(2.0, speed));
    setState(prev => ({ ...prev, speed: clampedSpeed }));
    // log.debug("speechOutput", `⚡ Velocidad ajustada: ${clampedSpeed}`);
  }, []);

  // ========== GAME-SPECIFIC SPEECH ==========
  const speakGameMessage = useCallback(async (message: string, type: MessageType): Promise<void> => {
    let enhancedMessage = message;

    // Añadir contexto emocional según el tipo
    switch (type) {
      case "question":
        enhancedMessage = message; // Las preguntas van tal como están
        break;
      case "answer":
        enhancedMessage = message; // Las respuestas también
        break;
      case "hint":
        enhancedMessage = `Te doy una pista: ${message}`;
        break;
      case "victory":
        enhancedMessage = `¡Excelente! ${message}`;
        break;
      case "error":
        enhancedMessage = `Lo siento, ${message}`;
        break;
      case "system":
        enhancedMessage = message;
        break;
    }

    // log.info("speechOutput", `🎮 Speaking game message [${type}]: ${enhancedMessage.substring(0, 50)}...`);

    const gameMessage = createMessage(enhancedMessage, type);
    addToHistory(gameMessage);

    await speak(enhancedMessage);
  }, [speak, createMessage, addToHistory]);

  const speakWithEmotion = useCallback(async (
    message: string,
    emotion: "excited" | "thoughtful" | "confident" | "disappointed"
  ): Promise<void> => {
    let emotionalMessage = message;

    switch (emotion) {
      case "excited":
        emotionalMessage = `¡${message}!`;
        break;
      case "thoughtful":
        emotionalMessage = `Hmm... ${message}`;
        break;
      case "confident":
        emotionalMessage = `Estoy segura: ${message}`;
        break;
      case "disappointed":
        emotionalMessage = `Oh... ${message}`;
        break;
    }

    // log.info("speechOutput", `😊 Speaking with emotion [${emotion}]: ${message.substring(0, 50)}...`);
    await speak(emotionalMessage);
  }, [speak]);

  // ========== CHARACTER VOICES ==========
  const getVoiceForCharacter = useCallback((character: string): VoiceGender => {
    const normalizedChar = character.toLowerCase();

    // Buscar coincidencia exacta
    for (const [charName, gender] of Object.entries(CHARACTER_VOICES)) {
      if (normalizedChar.includes(charName)) {
        return gender as VoiceGender;
      }
    }

    // Fallback: usar patrones de género
    const malePatterns = ["mr", "señor", "king", "prince", "man", "boy"];
    const femalePatterns = ["ms", "mrs", "señora", "queen", "princess", "woman", "girl"];

    if (malePatterns.some(pattern => normalizedChar.includes(pattern))) {
      return "male";
    }

    if (femalePatterns.some(pattern => normalizedChar.includes(pattern))) {
      return "female";
    }

    // Default: female (Aura's voice)
    return "female";
  }, []);

  const setCharacterVoice = useCallback(async (character: string): Promise<boolean> => {
    const gender = getVoiceForCharacter(character);
    // log.info("speechOutput", `🎭 Configurando voz para personaje: ${character} → ${gender}`);

    return await configure(gender);
  }, [getVoiceForCharacter, configure]);

  // ========== QUEUE MANAGEMENT ==========
  const speakQueue = useCallback(async (messages: string[]): Promise<void> => {
    if (isProcessingQueue) {
      // log.warn("speechOutput", "⚠️ Ya se está procesando una cola");
      return;
    }

    // log.info("speechOutput", `📋 Procesando cola de ${messages.length} mensajes`);
    setMessageQueue(messages);
    setIsProcessingQueue(true);

    try {
      for (const message of messages) {
        if (messageQueue.length === 0) break; // Cola cancelada

        await speak(message);
        setMessageQueue(prev => prev.slice(1)); // Remover mensaje procesado

        // Pequeña pausa entre mensajes
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      // log.error("speechOutput", "❌ Error procesando cola", error);
    } finally {
      setIsProcessingQueue(false);
      setMessageQueue([]);
    }
  }, [isProcessingQueue, speak, messageQueue]);

  const clearQueue = useCallback(() => {
    // log.info("speechOutput", "🗑️ Limpiando cola de mensajes");
    setMessageQueue([]);
    setIsProcessingQueue(false);
  }, []);

  const skipCurrent = useCallback(() => {
    // log.info("speechOutput", "⏭️ Saltando mensaje actual");
    stop();
    if (messageQueue.length > 1) {
      setMessageQueue(prev => prev.slice(1));
    } else {
      clearQueue();
    }
  }, [stop, messageQueue, clearQueue]);

  // ========== UTILITIES ==========
  const getAvailableVoices = useCallback((): string[] => {
    return speechService.getAvailableVoicesList();
  }, []);

  const testVoice = useCallback(async (text: string = "Hola, esta es una prueba de voz"): Promise<void> => {
    // log.info("speechOutput", "🧪 Probando voz actual");
    await speak(text);
  }, [speak]);

  const reset = useCallback(() => {
    // log.info("speechOutput", "🔄 Reseteando SpeechOutput");

    stop();
    clearQueue();

    setState({
      isReady: false,
      isConfiguring: false,
      playbackState: "idle",
      currentVoice: "",
      availableVoices: [],
      volume: 1.0,
      speed: 1.1,
      errorMessage: null,
    });

    setMessageHistory([]);
  }, [stop, clearQueue]);

  // ========== MESSAGE HISTORY ==========
  const getLastMessage = useCallback((): SpeechMessage | null => {
    return messageHistory[0] || null;
  }, [messageHistory]);

  const replayLastMessage = useCallback(async (): Promise<void> => {
    const lastMessage = getLastMessage();
    if (lastMessage) {
      // log.info("speechOutput", "🔁 Repitiendo último mensaje");
      await speak(lastMessage.text);
    } else {
      // log.warn("speechOutput", "⚠️ No hay mensajes para repetir");
    }
  }, [getLastMessage, speak]);

  // ========== CONTEXT VALUE ==========
  const contextValue: SpeechOutputContextProps = {
    // Current state
    state,

    // Basic speech controls
    speak,
    pause,
    resume,
    stop,

    // Configuration
    configure,
    setVolume,
    setSpeed,

    // Game-specific speech
    speakGameMessage,
    speakWithEmotion,

    // Character voices
    setCharacterVoice,
    getVoiceForCharacter,

    // Queue management
    speakQueue,
    clearQueue,
    skipCurrent,

    // Utilities
    getAvailableVoices,
    testVoice,
    reset,

    // Message history
    messageHistory,
    getLastMessage,
    replayLastMessage,
  };

  return (
    <SpeechOutputContext.Provider value={contextValue}>
      {children}
    </SpeechOutputContext.Provider>
  );
};
