import {
  createContext,
  useContext,
  useState,
  useCallback,
  type ReactNode,
} from "react";
import { aiService, type GameMode } from "../services/AIService";
import { log } from "../services/LogService";

// ========================================
// TIPOS Y INTERFACES
// ========================================

/**
 * Fases del juego que determinan qué acciones están disponibles.
 * - setup: Preparación inicial (selección de modo, personaje)
 * - questioning: Fase de preguntas (usuario hace preguntas o responde)
 * - guessing: Fase de suposiciones (usuario intenta adivinar el personaje)
 * - finished: Juego terminado (se muestra resultado)
 */
export type GamePhase = "setup" | "questioning" | "guessing" | "finished";

/**
 * Rol del jugador en la sesión actual
 * - guesser: El jugador hace preguntas (modo "ia_vs_player")
 * - answerer: El jugador responde preguntas (modo "player_vs_ia")
 */
export type PlayerRole = "guesser" | "answerer";

/**
 * Estructura principal de una sesión de juego
 * Contiene todo el estado necesario para una partida completa
 */
export interface GameSession {
  /** Identificador único de la sesión */
  id: string;
  /** Modo de juego seleccionado */
  mode: GameMode;
  /** Fase actual del juego */
  phase: GamePhase;
  /** Momento de inicio del juego */
  startTime: Date;
  /** Momento de finalización (si ha terminado) */
  endTime?: Date;

  // === ESTADO DEL JUEGO ===
  /** Número de preguntas realizadas */
  questionCount: number;
  /** Máximo de preguntas permitidas */
  maxQuestions: number;
  /** Personaje actual (si está definido) */
  currentCharacter?: string;
  /** Nivel de confianza de la IA (0-100) */
  aiConfidence: number;
  /** Rol del jugador en esta sesión */
  playerRole: PlayerRole;
  /** Historial completo de mensajes */
  messages: GameMessage[];

  // === RESULTADO DEL JUEGO ===
  /** Ganador de la partida */
  winner?: "ai" | "user" | "draw";
  /** Última suposición realizada */
  finalGuess?: string;
  /** Si la suposición final fue correcta */
  wasCorrect?: boolean;
}

/**
 * Estructura de un mensaje en el juego
 * Incluye metadatos para análisis y presentación
 */
export interface GameMessage {
  /** ID único del mensaje */
  id: string;
  /** Contenido del mensaje */
  text: string;
  /** Quién envió el mensaje */
  sender: "user" | "ai";
  /** Momento del envío */
  timestamp: Date;
  /** Tipo de mensaje para clasificación */
  type: "question" | "answer" | "guess" | "hint" | "system";
  /** Nivel de confianza (solo para mensajes de IA) */
  confidence?: number;
  /** Respuesta validada (solo para respuestas del usuario) */
  validatedResponse?: "yes" | "no" | "maybe" | "unknown";
}

/**
 * Insights generados por IA para ayudar al jugador
 * Proporciona sugerencias y análisis del progreso
 */
export interface GameInsight {
  /** Preguntas sugeridas por la IA */
  suggestedQuestions: string[];

  /** Progreso en diferentes categorías de información */
  categoryProgress: {
    person: boolean;      // ¿Se preguntó si es una persona?
    profession: boolean;  // ¿Se preguntó sobre profesión?
    appearance: boolean;  // ¿Se preguntó sobre apariencia?
    era: boolean;         // ¿Se preguntó sobre época?
    nationality: boolean; // ¿Se preguntó sobre nacionalidad?
  };

  /** Personajes más probables según el análisis */
  likelyCharacters: string[];

  /** Nivel de confianza general (0-100) */
  confidenceLevel: number;
}

// ========================================
// INTERFAZ DEL CONTEXTO
// ========================================

/**
 * Propiedades y métodos disponibles en el contexto del juego
 */
interface EnygmaGameContextProps {
  // === ESTADO ACTUAL ===
  /** Sesión de juego actual (null si no hay juego activo) */
  session: GameSession | null;
  /** Fase actual del juego */
  currentPhase: GamePhase;
  /** Rol del jugador actual */
  playerRole: PlayerRole;

  // === ACCIONES DEL JUEGO ===
  /** Inicia una nueva partida */
  startNewGame: (mode: GameMode, character?: string) => Promise<void>;
  /** Envía una pregunta (cuando el usuario es guesser) */
  askQuestion: (question: string) => Promise<void>;
  /** Responde a una pregunta (cuando el usuario es answerer) */
  respondToQuestion: (
    response: "yes" | "no" | "maybe" | "unknown"
  ) => Promise<void>;
  /** Realiza una suposición sobre el personaje */
  makeGuess: (character: string) => Promise<boolean>;
  /** Termina el juego con una razón específica */
  endGame: (reason: "victory" | "defeat" | "timeout" | "quit") => void;

  // === INTELIGENCIA DEL JUEGO ===
  /** Obtiene insights y sugerencias de la IA */
  getGameInsights: () => GameInsight;
  /** Obtiene respuestas sugeridas para el contexto actual */
  getSuggestedResponses: () => string[];
  /** Obtiene un resumen del progreso actual */
  getProgressSummary: () => string;

  // === PROPIEDADES COMPUTADAS ===
  /** Indica si se puede hacer una pregunta */
  canAskQuestion: boolean;
  /** Indica si se puede hacer una suposición */
  canMakeGuess: boolean;
  /** Preguntas restantes en la sesión */
  questionsRemaining: number;
  /** Progreso del juego en porcentaje (0-100) */
  gameProgress: number;

  // === UTILIDADES ===
  /** Valida la entrada del usuario */
  validateUserInput: (input: string) => {
    isValid: boolean;
    suggestion?: string;
  };
  /** Obtiene una pista contextual */
  getHint: () => string | null;
}

// ========================================
// CREACIÓN DEL CONTEXTO
// ========================================

const EnygmaGameContext = createContext<EnygmaGameContextProps | undefined>(
  undefined
);

/**
 * Hook para acceder al contexto del juego
 * Debe usarse dentro de un EnygmaGameProvider
 */
export const useEnygmaGame = () => {
  const context = useContext(EnygmaGameContext);
  if (!context) {
    throw new Error("useEnygmaGame must be used within EnygmaGameProvider");
  }
  return context;
};

// ========================================
// PROVIDER COMPONENT
// ========================================

/**
 * Proveedor del contexto del juego Enygma
 *
 * Gestiona todo el estado y la lógica del juego, incluyendo:
 * - Inicialización de nuevas partidas
 * - Manejo de preguntas y respuestas
 * - Cálculo de progreso y estadísticas
 * - Integración con servicios de IA
 * - Validación de entrada del usuario
 *
 * @param children - Componentes hijos que tendrán acceso al contexto
 */
export const EnygmaGameProvider = ({ children }: { children: ReactNode }) => {
  // === ESTADO PRINCIPAL ===
  const [session, setSession] = useState<GameSession | null>(null);

  // === PROPIEDADES COMPUTADAS ===

  /**
   * Fase actual del juego
   * Determina qué acciones están disponibles
   */
  const currentPhase: GamePhase = session?.phase || "setup";

  /**
   * Rol del jugador basado en el modo de juego
   * - "player_vs_ia": El usuario responde preguntas (answerer)
   * - "ia_vs_player": El usuario hace preguntas (guesser)
   */
  const playerRole: PlayerRole = session?.playerRole || "guesser";

  /**
   * Determina si el usuario puede hacer una pregunta
   * Requiere estar en fase de questioning y no haber agotado las preguntas
   */
  const canAskQuestion =
    session?.phase === "questioning" &&
    session.questionCount < session.maxQuestions &&
    session.playerRole === "guesser";

  /**
   * Determina si el usuario puede hacer una suposición
   * Disponible en fases de questioning y guessing
   */
  const canMakeGuess =
    session?.phase === "questioning" || session?.phase === "guessing";

  /**
   * Calcula preguntas restantes en la sesión
   */
  const questionsRemaining = session
    ? session.maxQuestions - session.questionCount
    : 20;

  /**
   * Calcula el progreso del juego en porcentaje
   */
  const gameProgress = session
    ? (session.questionCount / session.maxQuestions) * 100
    : 0;

  // === FUNCIONES PRINCIPALES ===

  /**
   * Inicia una nueva partida del juego
   *
   * @param mode - Modo de juego seleccionado
   * @param character - Personaje opcional (para modo "player_vs_ia")
   */
  const startNewGame = useCallback(
    async (mode: GameMode, character?: string): Promise<void> => {
      // log.info("game", `Iniciando nueva partida: ${mode}`, { character });

      try {
        // Crear nueva sesión con configuración inicial
        const newSession: GameSession = {
          id: `game-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          mode,
          phase: "questioning",
          startTime: new Date(),
          questionCount: 0,
          maxQuestions: 20,
          currentCharacter: character,
          aiConfidence: 0,
          messages: [],
          // Determinar rol del jugador según el modo
          playerRole: mode === "player_vs_ia" ? "answerer" : "guesser",
        };

        // Actualizar estado
        setSession(newSession);

        // log.success("game", "Partida iniciada exitosamente", {
        //   sessionId: newSession.id,
        //   playerRole: newSession.playerRole,
        // });
      } catch (error) {
        // log.error("game", "Error al iniciar partida", error);
        throw error;
      }
    },
    []
  );

  /**
   * Procesa una pregunta del usuario (cuando es guesser)
   *
   * @param question - Pregunta formulada por el usuario
   */
  const askQuestion = useCallback(
    async (question: string): Promise<void> => {
      // Validar estado del juego
      if (!session || !canAskQuestion) {
        // log.warn("game", "No se puede hacer pregunta en el estado actual");
        return;
      }

      // log.info("game", `Usuario preguntando: ${question}`);

      // Crear mensaje del usuario
      const userMessage: GameMessage = {
        id: `msg-${Date.now()}`,
        text: question,
        sender: "user",
        timestamp: new Date(),
        type: "question",
      };

      try {
        // Obtener respuesta de la IA
        const response = await aiService.generateResponse(
          question,
          session.mode
        );

        if (response.ok) {
          // Crear mensaje de respuesta de la IA
          const aiMessage: GameMessage = {
            id: `msg-${Date.now() + 1}`,
            text: response.output,
            sender: "ai",
            timestamp: new Date(),
            type: "answer",
          };

          // Actualizar sesión con ambos mensajes
          setSession((prev) => {
            if (!prev) return null;

            const newQuestionCount = prev.questionCount + 1;
            const newPhase = newQuestionCount >= prev.maxQuestions
              ? "guessing"
              : "questioning";

            return {
              ...prev,
              questionCount: newQuestionCount,
              phase: newPhase,
              messages: [...prev.messages, userMessage, aiMessage],
            };
          });

          // log.success("game", "Pregunta procesada exitosamente");
        } else {
          throw new Error("Error al obtener respuesta de la IA");
        }
      } catch (error) {
        // log.error("game", "Error al procesar pregunta", error);
        throw error;
      }
    },
    [session, canAskQuestion]
  );

  /**
   * Procesa una respuesta del usuario (cuando es answerer)
   *
   * @param response - Respuesta del usuario a la pregunta de la IA
   */
  const respondToQuestion = useCallback(
    async (response: "yes" | "no" | "maybe" | "unknown"): Promise<void> => {
      // Validar modo de juego
      if (!session || session.playerRole !== "answerer") {
        // log.warn("game", "No se puede responder en el modo actual");
        return;
      }

      // log.info("game", `Usuario respondiendo: ${response}`);

      // Mapear respuesta a texto legible
      const responseText = {
        yes: "Sí",
        no: "No",
        maybe: "Tal vez",
        unknown: "No lo sé",
      }[response];

      // Crear mensaje de respuesta del usuario
      const userMessage: GameMessage = {
        id: `msg-${Date.now()}`,
        text: responseText,
        sender: "user",
        timestamp: new Date(),
        type: "answer",
        validatedResponse: response,
      };

      try {
        // Obtener siguiente pregunta o suposición de la IA
        const aiResponse = await aiService.generateResponse(
          responseText,
          session.mode
        );

        if (aiResponse.ok) {
          // Determinar si es una suposición o pregunta
          const isGuess =
            aiResponse.output.toLowerCase().includes("creo que") ||
            aiResponse.output.toLowerCase().includes("es ") ||
            aiResponse.output.toLowerCase().includes("¿es ");

          // Calcular nueva confianza de la IA
          const confidenceChange = {
            yes: 10,
            no: -5,
            maybe: 2,
            unknown: 0
          }[response];

          const newConfidence = Math.max(
            0,
            Math.min(100, session.aiConfidence + confidenceChange)
          );

          // Crear mensaje de la IA
          const aiMessage: GameMessage = {
            id: `msg-${Date.now() + 1}`,
            text: aiResponse.output,
            sender: "ai",
            timestamp: new Date(),
            type: isGuess ? "guess" : "question",
            confidence: newConfidence,
          };

          // Actualizar sesión
          setSession((prev) => {
            if (!prev) return null;

            const newQuestionCount = prev.questionCount + 1;
            const newPhase = isGuess
              ? "guessing"
              : newQuestionCount >= prev.maxQuestions
                ? "guessing"
                : "questioning";

            return {
              ...prev,
              questionCount: newQuestionCount,
              phase: newPhase,
              aiConfidence: newConfidence,
              messages: [...prev.messages, userMessage, aiMessage],
            };
          });

          // log.success("game", "Respuesta procesada exitosamente");
        }
      } catch (error) {
        // log.error("game", "Error al procesar respuesta", error);
        throw error;
      }
    },
    [session]
  );

  /**
   * Procesa una suposición sobre el personaje
   *
   * @param character - Nombre del personaje supuesto
   * @returns Si la suposición fue correcta
   */
  const makeGuess = useCallback(
    async (character: string): Promise<boolean> => {
      if (!session) return false;

      // log.info("game", `Realizando suposición: ${character}`);

      // Crear mensaje de suposición
      const guessMessage: GameMessage = {
        id: `msg-${Date.now()}`,
        text: `¿Es ${character}?`,
        sender: session.playerRole === "guesser" ? "user" : "ai",
        timestamp: new Date(),
        type: "guess",
      };

      try {
        // TODO: Implementar validación real contra personaje objetivo
        // Por ahora usamos probabilidad basada en confianza de la IA
        const successProbability = session.aiConfidence / 100;
        const isCorrect = Math.random() < successProbability;

        // Determinar ganador
        const winner = isCorrect
          ? (session.playerRole === "guesser" ? "user" : "ai")
          : (session.playerRole === "guesser" ? "ai" : "user");

        // Finalizar sesión
        setSession((prev) => {
          if (!prev) return null;

          return {
            ...prev,
            phase: "finished",
            endTime: new Date(),
            messages: [...prev.messages, guessMessage],
            winner,
            finalGuess: character,
            wasCorrect: isCorrect,
          };
        });

        // log.success("game", "Suposición procesada", {
        //   character,
        //   isCorrect,
        //   winner
        // });

        return isCorrect;
      } catch (error) {
        // log.error("game", "Error al procesar suposición", error);
        return false;
      }
    },
    [session]
  );

  /**
   * Finaliza el juego con una razón específica
   *
   * @param reason - Razón por la que termina el juego
   */
  const endGame = useCallback(
    (reason: "victory" | "defeat" | "timeout" | "quit") => {
      if (!session) return;

      // log.info("game", `Terminando juego: ${reason}`, {
      //   sessionId: session.id
      // });

      // Mapear razón a ganador
      const winner = {
        victory: "user",
        defeat: "ai",
        timeout: undefined,
        quit: undefined,
      }[reason] as "user" | "ai" | undefined;

      setSession((prev) => {
        if (!prev) return null;

        return {
          ...prev,
          phase: "finished",
          endTime: new Date(),
          winner,
        };
      });
    },
    [session]
  );

  // === FUNCIONES DE ANÁLISIS ===

  /**
   * Genera insights del juego basados en el historial de mensajes
   * Analiza qué categorías se han explorado y sugiere próximos pasos
   */
  const getGameInsights = useCallback((): GameInsight => {
    if (!session) {
      return {
        suggestedQuestions: [],
        categoryProgress: {
          person: false,
          profession: false,
          appearance: false,
          era: false,
          nationality: false,
        },
        likelyCharacters: [],
        confidenceLevel: 0,
      };
    }

    // Analizar mensajes para determinar qué se ha preguntado
    const messages = session.messages;
    const askedAbout = {
      person: messages.some(m =>
        m.text.toLowerCase().includes("persona") ||
        m.text.toLowerCase().includes("real") ||
        m.text.toLowerCase().includes("fictici")
      ),
      profession: messages.some(m =>
        m.text.toLowerCase().includes("actor") ||
        m.text.toLowerCase().includes("trabajo") ||
        m.text.toLowerCase().includes("profesión")
      ),
      appearance: messages.some(m =>
        m.text.toLowerCase().includes("pelo") ||
        m.text.toLowerCase().includes("alto") ||
        m.text.toLowerCase().includes("apariencia")
      ),
      era: messages.some(m =>
        m.text.toLowerCase().includes("vivo") ||
        m.text.toLowerCase().includes("siglo") ||
        m.text.toLowerCase().includes("época")
      ),
      nationality: messages.some(m =>
        m.text.toLowerCase().includes("americano") ||
        m.text.toLowerCase().includes("país") ||
        m.text.toLowerCase().includes("nacionalidad")
      ),
    };

    // Generar sugerencias basadas en lo que falta explorar
    const suggestions: string[] = [];
    if (!askedAbout.person) suggestions.push("¿Es una persona real?");
    if (!askedAbout.profession) suggestions.push("¿Es actor o actriz?");
    if (!askedAbout.era) suggestions.push("¿Está vivo actualmente?");
    if (!askedAbout.nationality) suggestions.push("¿Es de Estados Unidos?");
    if (!askedAbout.appearance) suggestions.push("¿Tiene el pelo oscuro?");

    return {
      suggestedQuestions: suggestions,
      categoryProgress: askedAbout,
      likelyCharacters: [], // Se poblaría con análisis de IA más avanzado
      confidenceLevel: session.aiConfidence,
    };
  }, [session]);

  /**
   * Obtiene respuestas sugeridas según el contexto actual
   */
  const getSuggestedResponses = useCallback((): string[] => {
    if (!session) return [];

    if (session.playerRole === "answerer") {
      // Respuestas estándar para el modo answerer
      return ["Sí", "No", "Tal vez", "No lo sé"];
    } else {
      // Preguntas sugeridas para el modo guesser
      const insights = getGameInsights();
      return insights.suggestedQuestions.slice(0, 3);
    }
  }, [session, getGameInsights]);

  /**
   * Valida la entrada del usuario según el contexto
   */
  const validateUserInput = useCallback(
    (input: string): { isValid: boolean; suggestion?: string } => {
      // Validar entrada vacía
      if (!input.trim()) {
        return {
          isValid: false,
          suggestion: "Por favor, escribe algo"
        };
      }

      // Validar longitud
      if (input.length > 200) {
        return {
          isValid: false,
          suggestion: "El texto es muy largo. Sé más conciso.",
        };
      }

      // Validaciones específicas para modo answerer
      if (session?.playerRole === "answerer") {
        const validResponses = [
          "sí", "si", "no", "tal vez", "quizás",
          "no lo sé", "no sé", "puede ser"
        ];

        const isValidResponse = validResponses.some(response =>
          input.toLowerCase().includes(response)
        );

        if (!isValidResponse) {
          return {
            isValid: false,
            suggestion: "Responde solo con: Sí, No, Tal vez, o No lo sé",
          };
        }
      }

      return { isValid: true };
    },
    [session]
  );

  /**
   * Genera una pista contextual para ayudar al jugador
   */
  const getHint = useCallback((): string | null => {
    if (!session) return null;

    const insights = getGameInsights();
    const questionCount = session.questionCount;

    // Pistas según el progreso del juego
    if (questionCount < 5) {
      return "Empieza con preguntas amplias para descartar categorías grandes";
    }

    if (questionCount > 15) {
      return "¡Te quedan pocas preguntas! Es hora de hacer suposiciones específicas";
    }

    if (insights.suggestedQuestions.length > 0) {
      return `Considera preguntar: ${insights.suggestedQuestions[0]}`;
    }

    if (session.aiConfidence > 70) {
      return "La IA parece muy confiada. ¡Podrías arriesgarte con una suposición!";
    }

    return null;
  }, [session, getGameInsights]);

  /**
   * Genera un resumen del progreso actual
   */
  const getProgressSummary = useCallback((): string => {
    if (!session) return "";

    const progress = Math.round(gameProgress);
    return `Pregunta ${session.questionCount}/${session.maxQuestions} - ${progress}% completado`;
  }, [session, gameProgress]);

  // === VALOR DEL CONTEXTO ===
  const contextValue: EnygmaGameContextProps = {
    // Estado
    session,
    currentPhase,
    playerRole,

    // Acciones
    startNewGame,
    askQuestion,
    respondToQuestion,
    makeGuess,
    endGame,

    // Análisis
    getGameInsights,
    getSuggestedResponses,
    getProgressSummary,

    // Propiedades computadas
    canAskQuestion,
    canMakeGuess,
    questionsRemaining,
    gameProgress,

    // Utilidades
    validateUserInput,
    getHint,
  };

  return (
    <EnygmaGameContext.Provider value={contextValue}>
      {children}
    </EnygmaGameContext.Provider>
  );
};
