// components/CookieConsentBanner.tsx (CORREGIDO)
import { useState, useEffect, useCallback } from "react";
import { useSpeechOutput } from "../contexts/SpeechOutputContext"; // ✅ Contexto correcto

interface CookieConsentBannerProps {
  onAudioActivated?: () => void;
}

export const CookieConsentBanner: React.FC<CookieConsentBannerProps> = ({
  onAudioActivated,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isActivatingAudio, setIsActivatingAudio] = useState(false);

  // ✅ Usar el contexto correcto SpeechOutputContext
  const {
    speakGameMessage,
    configure,
    state: { isReady: isLocutionActivated }
  } = useSpeechOutput();

  // Verificar si ya se dió consentimiento
  useEffect(() => {
    const hasConsent = localStorage.getItem("enygma_analytics_consent");
    const hasAudioActivated = localStorage.getItem("enygma_audio_activated");

    // Solo mostrar banner si no hay consentimiento Y no se ha activado el audio
    const shouldShowBanner = !hasConsent || (!hasAudioActivated && !isLocutionActivated);

    if (shouldShowBanner) {
      setTimeout(() => setIsVisible(true), 1500); // Delay para que cargue la página
    }
  }, [isLocutionActivated]);

  const activateAudioWithConsent = useCallback(
    async (acceptAnalytics: boolean) => {
      setIsActivatingAudio(true);

      try {
        // Guardar preferencias de analíticas
        localStorage.setItem(
          "enygma_analytics_consent",
          acceptAnalytics ? "accepted" : "rejected",
        );
        localStorage.setItem(
          "enygma_analytics_timestamp",
          new Date().toISOString(),
        );

        // ✅ Configurar voz primero si no está lista
        if (!isLocutionActivated) {
          // console.log("🔧 Configurando voz para banner...");
          await configure("female");
        }

        // ✅ Usar speakGameMessage
        if (speakGameMessage) {
          await speakGameMessage(
            "Bienvenido a Enygma, ¿lo adivinas?",
            "system",
          );
          localStorage.setItem("enygma_audio_activated", "true");
        }

        // Ocultar banner
        setIsVisible(false);

        // Callback opcional
        onAudioActivated?.();

        // Si acepta analíticas
        if (acceptAnalytics) {
          // console.log("🍪 Analíticas aceptadas");
        } else {
          // console.log("🍪 Analíticas rechazadas - modo privado");
        }
      } catch (error) {
        // console.error("❌ Error activando audio:", error);
        setIsVisible(false);
      } finally {
        setIsActivatingAudio(false);
      }
    },
    [speakGameMessage, onAudioActivated, configure, isLocutionActivated],
  );

  const handleReject = useCallback(() => {
    // Solo guardar preferencias sin activar audio
    localStorage.setItem("enygma_analytics_consent", "rejected");
    localStorage.setItem("enygma_analytics_timestamp", new Date().toISOString());

    // Ocultar banner inmediatamente
    setIsVisible(false);

    // console.log("🍪 Analíticas rechazadas - modo privado");
  }, []);

  const handleAccept = () => activateAudioWithConsent(true);

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay */}
      <div
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          zIndex: 9998,
          backdropFilter: "blur(2px)",
        }}
      />

      {/* Banner */}
      <div
        style={{
          position: "fixed",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: "90%",
          maxWidth: "480px",
          backgroundColor: "#1e293b", // Fondo oscuro como en la imagen
          borderRadius: "12px",
          padding: "32px",
          boxShadow: "0 20px 60px rgba(0, 0, 0, 0.3)",
          zIndex: 9999,
          animation: "slideUp 0.5s ease-out",
          textAlign: "center",
        }}
      >
        {/* Título */}
        <h2
          style={{
            margin: "0 0 16px 0",
            fontSize: "24px",
            fontWeight: "600",
            color: "#ffffff",
            lineHeight: "1.3",
          }}
        >
          Ayúdanos a mejorar
        </h2>

        {/* Descripción */}
        <p
          style={{
            margin: "0 0 32px 0",
            fontSize: "16px",
            color: "#94a3b8",
            lineHeight: "1.5",
            maxWidth: "400px",
            marginLeft: "auto",
            marginRight: "auto",
          }}
        >
          Al aceptar nos ayudas a mejorar la experiencia permitiendo el análisis
          estadístico de datos de uso.
        </p>

        {/* Botones */}
        <div
          style={{
            display: "flex",
            gap: "16px",
            justifyContent: "center",
            flexDirection: window.innerWidth < 480 ? "column" : "row",
          }}
        >
          {/* Botón Rechazar */}
          <button
            onClick={handleReject}
            disabled={isActivatingAudio}
            style={{
              minWidth: "120px",
              backgroundColor: "transparent",
              color: "#ffffff",
              border: "2px solid #475569",
              borderRadius: "6px",
              padding: "12px 24px",
              fontSize: "16px",
              fontWeight: "500",
              cursor: isActivatingAudio ? "not-allowed" : "pointer",
              transition: "all 0.2s ease",
              opacity: isActivatingAudio ? 0.5 : 1,
            }}
            onMouseEnter={(e) => {
              if (!isActivatingAudio) {
                e.currentTarget.style.backgroundColor = "#475569";
                e.currentTarget.style.borderColor = "#64748b";
              }
            }}
            onMouseLeave={(e) => {
              if (!isActivatingAudio) {
                e.currentTarget.style.backgroundColor = "transparent";
                e.currentTarget.style.borderColor = "#475569";
              }
            }}
          >
            Rechazar
          </button>

          {/* Botón Aceptar */}
          <button
            onClick={handleAccept}
            disabled={isActivatingAudio}
            style={{
              minWidth: "120px",
              backgroundColor: isActivatingAudio ? "#1e40af" : "#2563eb",
              color: "#ffffff",
              border: "none",
              borderRadius: "6px",
              padding: "12px 24px",
              fontSize: "16px",
              fontWeight: "500",
              cursor: isActivatingAudio ? "not-allowed" : "pointer",
              transition: "background-color 0.2s ease",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              gap: "8px",
            }}
            onMouseEnter={(e) => {
              if (!isActivatingAudio) {
                e.currentTarget.style.backgroundColor = "#1d4ed8";
              }
            }}
            onMouseLeave={(e) => {
              if (!isActivatingAudio) {
                e.currentTarget.style.backgroundColor = "#2563eb";
              }
            }}
          >
            {isActivatingAudio ? (
              <>
                <div
                  style={{
                    width: "16px",
                    height: "16px",
                    border: "2px solid #ffffff30",
                    borderTop: "2px solid #ffffff",
                    borderRadius: "50%",
                    animation: "spin 1s linear infinite",
                  }}
                />
                Activando...
              </>
            ) : (
              "Aceptar"
            )}
          </button>
        </div>
      </div>

      {/* CSS Animations */}
      <style>{`
        @keyframes slideUp {
          from {
            transform: translate(-50%, -50%) translateY(100%);
            opacity: 0;
          }
          to {
            transform: translate(-50%, -50%) translateY(0);
            opacity: 1;
          }
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </>
  );
};
